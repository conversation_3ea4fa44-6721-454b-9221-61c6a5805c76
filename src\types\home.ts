export interface IHome {
    id: string;
    createdAt: string;
    updatedAt: string;
}

export interface IHero {
    id: number;
    videoUrl: string;
    titles: string[];
    subtitles: string[];
    images: string[];
}

export interface IAdventure {
    id: string;
    title: string;
    images: string[];
    points: string[];
    linkUrl: string;
    linkLabel: string;
}

export interface IExperienceFeature {
    title: string;
    subtitle: string;
}

export interface IExperience {
    id: string;
    heading: string;
    subHeading: string;
    homeId: string;
    features: IExperienceFeature[];
}

export interface IHikingAreaItem {
    id: string;
    homeHikingId: string;
    image: string;      
    title: string;
    subtitle: string;
    linkUrl: string;
    createdAt: string;
    updatedAt: string;
}

export interface IHikingArea {
    id: string;
    heading: string;
    subheading: string;
    homeId: string;
    createdAt: string;
    updatedAt: string;
    areas: IHikingAreaItem[];
}

export interface ITailoredAdventure {
    id: number;
    title: string;
    subtitle: string;
    buttonText: string;
    buttonLink: string;
}

export interface IReview {
    id: number;
    imageFile?: File;
    imagePreview?: string;
    text: string;
    name: string;
    destination: string;
    date: string;
}


export interface ITestimonial {
    id: number;
    youtubeUrl: string;
    title: string;
    destination: string;
    date: string;
}
