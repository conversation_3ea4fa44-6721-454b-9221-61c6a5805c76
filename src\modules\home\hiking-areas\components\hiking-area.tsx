"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { UseGetHikingArea } from "../queries/use-get-hiking-area";

const HikingAreasListPage: React.FC = () => {
  const { data, isLoading, error } = UseGetHikingArea();
  const router = useRouter();

  if (isLoading) return <p>Loading hiking areas...</p>;
  if (error) return <p>Error loading hiking areas: {error.message}</p>;

  const areas = data?.data || [];

  return (
    <div className="p-6 max-w-5xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Hiking Areas</h1>
        <Button onClick={() => router.push("/hiking-areas/create")}>+ Add Hiking Area</Button>
      </div>

      <table className="min-w-full border border-gray-300">
        <thead>
          <tr className="bg-gray-100">
            <th className="border px-4 py-2">Image</th>
            <th className="border px-4 py-2">Title</th>
            <th className="border px-4 py-2">Subtitle</th>
            <th className="border px-4 py-2">Link</th>
            <th className="border px-4 py-2"></th>
          </tr>
        </thead>
        <tbody>
          {areas.map(area => (
            <tr key={area.id} className="even:bg-gray-50">
              <td className="border px-4 py-2">
                {area.imagePreview && (
                  <Image src={area.imagePreview} alt={area.title} width={100} height={60} className="rounded object-cover" />
                )}
              </td>
              <td className="border px-4 py-2">{area.title}</td>
              <td className="border px-4 py-2">{area.subtitle}</td>
              <td className="border px-4 py-2">
                <a href={area.linkUrl} className="text-blue-600 hover:underline" target="_blank" rel="noreferrer">
                  {area.linkUrl}
                </a>
              </td>
              <td className="border px-4 py-2 space-x-2">
                <Button size="sm" onClick={() => router.push(`/hiking-areas/edit/${area.id}`)}>
                  Edit
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default HikingAreasListPage;
